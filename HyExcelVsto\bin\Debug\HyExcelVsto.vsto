﻿<?xml version="1.0" encoding="utf-8"?>
<asmv1:assembly xsi:schemaLocation="urn:schemas-microsoft-com:asm.v1 assembly.adaptive.xsd" manifestVersion="1.0" xmlns:asmv1="urn:schemas-microsoft-com:asm.v1" xmlns="urn:schemas-microsoft-com:asm.v2" xmlns:asmv2="urn:schemas-microsoft-com:asm.v2" xmlns:xrml="urn:mpeg:mpeg21:2003:01-REL-R-NS" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:asmv3="urn:schemas-microsoft-com:asm.v3" xmlns:dsig="http://www.w3.org/2000/09/xmldsig#" xmlns:co.v1="urn:schemas-microsoft-com:clickonce.v1" xmlns:co.v2="urn:schemas-microsoft-com:clickonce.v2">
  <assemblyIdentity name="HyExcelVsto.vsto" version="********" publicKeyToken="6dec67056def2682" language="neutral" processorArchitecture="msil" xmlns="urn:schemas-microsoft-com:asm.v1" />
  <description asmv2:publisher="HyExcelVsto" asmv2:product="HyExcelVsto" xmlns="urn:schemas-microsoft-com:asm.v1" />
  <deployment install="false" />
  <compatibleFrameworks xmlns="urn:schemas-microsoft-com:clickonce.v2">
    <framework targetVersion="4.8" profile="Full" supportedRuntime="4.0.30319" />
  </compatibleFrameworks>
  <dependency>
    <dependentAssembly dependencyType="install" codebase="HyExcelVsto.dll.manifest" size="84599">
      <assemblyIdentity name="HyExcelVsto.dll" version="********" publicKeyToken="6dec67056def2682" language="neutral" processorArchitecture="msil" type="win32" />
      <hash>
        <dsig:Transforms>
          <dsig:Transform Algorithm="urn:schemas-microsoft-com:HashTransforms.Identity" />
        </dsig:Transforms>
        <dsig:DigestMethod Algorithm="http://www.w3.org/2000/09/xmldsig#sha256" />
        <dsig:DigestValue>b+0igAW24gMLOUdKM+cuXlX39DAXiJQ9MR7uwfJLJek=</dsig:DigestValue>
      </hash>
    </dependentAssembly>
  </dependency>
<publisherIdentity name="CN=DESKTOP-QUE4EMJ\HHY" issuerKeyHash="76f8fef03be9e916fdc6442164bff7e6db87a865" /><Signature Id="StrongNameSignature" xmlns="http://www.w3.org/2000/09/xmldsig#"><SignedInfo><CanonicalizationMethod Algorithm="http://www.w3.org/2001/10/xml-exc-c14n#" /><SignatureMethod Algorithm="http://www.w3.org/2000/09/xmldsig#rsa-sha256" /><Reference URI=""><Transforms><Transform Algorithm="http://www.w3.org/2000/09/xmldsig#enveloped-signature" /><Transform Algorithm="http://www.w3.org/2001/10/xml-exc-c14n#" /></Transforms><DigestMethod Algorithm="http://www.w3.org/2000/09/xmldsig#sha256" /><DigestValue>9vmTf7del/cVVl34BfJ6EzSB4wdf7XQZJvdbjdC/kxY=</DigestValue></Reference></SignedInfo><SignatureValue>aGwqK9tMxu9qe1HPhJX1vBpFanaoJG6aQZDAHLO+FmgDUO9MqfkG/eBVdWjYJe23PnRGDwKnNoldbRqw5gcVxNzWtxY/LAGVaxMtHlME/yKfoaEln+6d+eHoWjacDomFYBL+piyJd7mo3vNGYZx798kEbd+KCWgkTl5CUwVsAF8=</SignatureValue><KeyInfo Id="StrongNameKeyInfo"><KeyValue><RSAKeyValue><Modulus>xYfYzcC3fdVJvs7+69QTUGhjXJi3gWoDsTS8KXwn8LL2lMZcQ78hC0D+ZDyoSo72CNR6S8FPDTl34NlsQoT9V4n7DGFPbK4b99u5gcvVUk03T2p9l+UhznPZ6ibKP0BwHwi6z7E7GfuRRSwxDRohtkeN1B8mqLSpfq7Ldi9ERs0=</Modulus><Exponent>AQAB</Exponent></RSAKeyValue></KeyValue><msrel:RelData xmlns:msrel="http://schemas.microsoft.com/windows/rel/2005/reldata"><r:license xmlns:r="urn:mpeg:mpeg21:2003:01-REL-R-NS" xmlns:as="http://schemas.microsoft.com/windows/pki/2005/Authenticode"><r:grant><as:ManifestInformation Hash="1693bfd08d5bf7261974ed5f07e38134137af205f85d5615f7975eb77f93f9f6" Description="" Url=""><as:assemblyIdentity name="HyExcelVsto.vsto" version="********" publicKeyToken="6dec67056def2682" language="neutral" processorArchitecture="msil" xmlns="urn:schemas-microsoft-com:asm.v1" /></as:ManifestInformation><as:SignedBy /><as:AuthenticodePublisher><as:X509SubjectName>CN=DESKTOP-QUE4EMJ\HHY</as:X509SubjectName></as:AuthenticodePublisher></r:grant><r:issuer><Signature Id="AuthenticodeSignature" xmlns="http://www.w3.org/2000/09/xmldsig#"><SignedInfo><CanonicalizationMethod Algorithm="http://www.w3.org/2001/10/xml-exc-c14n#" /><SignatureMethod Algorithm="http://www.w3.org/2000/09/xmldsig#rsa-sha256" /><Reference URI=""><Transforms><Transform Algorithm="http://www.w3.org/2000/09/xmldsig#enveloped-signature" /><Transform Algorithm="http://www.w3.org/2001/10/xml-exc-c14n#" /></Transforms><DigestMethod Algorithm="http://www.w3.org/2000/09/xmldsig#sha256" /><DigestValue>5eUCZ5EgzCFbwKlrFCgUzti7QYJuk3xQSZT5AP1d9Og=</DigestValue></Reference></SignedInfo><SignatureValue>XVkpcMkCdF8X0146Dp8miLUYgtzwZb60Yx7uMVIj0pnVieq5ze/i4mvYeu2BuaZeYn1quWRfse5M4SGj5OHOBI7l4ZStQI50n+I2spBTHs2+RltNiFUDyoVR76alwvaLaywT6AV6cyqtDglop/L2p0c2Ph5Rh7GdsNZh/B3EwK4=</SignatureValue><KeyInfo><KeyValue><RSAKeyValue><Modulus>xYfYzcC3fdVJvs7+69QTUGhjXJi3gWoDsTS8KXwn8LL2lMZcQ78hC0D+ZDyoSo72CNR6S8FPDTl34NlsQoT9V4n7DGFPbK4b99u5gcvVUk03T2p9l+UhznPZ6ibKP0BwHwi6z7E7GfuRRSwxDRohtkeN1B8mqLSpfq7Ldi9ERs0=</Modulus><Exponent>AQAB</Exponent></RSAKeyValue></KeyValue><X509Data><X509Certificate>MIIB5TCCAU6gAwIBAgIQKuMtYyMoKIdEv4fC44j4tDANBgkqhkiG9w0BAQsFADAxMS8wLQYDVQQDHiYARABFAFMASwBUAE8AUAAtAFEAVQBFADQARQBNAEoAXABIAEgAWTAeFw0yNTA2MTcwODA4MTJaFw0yNjA2MTcxNDA4MTJaMDExLzAtBgNVBAMeJgBEAEUAUwBLAFQATwBQAC0AUQBVAEUANABFAE0ASgBcAEgASABZMIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDFh9jNwLd91Um+zv7r1BNQaGNcmLeBagOxNLwpfCfwsvaUxlxDvyELQP5kPKhKjvYI1HpLwU8NOXfg2WxChP1XifsMYU9srhv327mBy9VSTTdPan2X5SHOc9nqJso/QHAfCLrPsTsZ+5FFLDENGiG2R43UHyaotKl+rst2L0RGzQIDAQABMA0GCSqGSIb3DQEBCwUAA4GBACra0QfchCVF4U3hVlokQR8jrULaqburZnwTAzXudUBhelLojyfaUc7aOyMjS+fLsqfqVY8DY9N5em/YreXeTruaT57312t4c86BCkjqskQTJcBJmZaAzjD4oAhfXK6SK+RtiEUiVR6/q9vCzy9FwYadj9LV2RPbmU3D7zdywQsU</X509Certificate></X509Data></KeyInfo></Signature></r:issuer></r:license></msrel:RelData></KeyInfo></Signature></asmv1:assembly>